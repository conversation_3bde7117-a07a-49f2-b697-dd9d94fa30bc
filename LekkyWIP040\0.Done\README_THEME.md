# NewLekky Theme Implementation Guide

This guide explains how to use the theme system in the NewLekky app. It covers the color palette, theme configuration, and implementation details.

## Table of Contents

1. [Overview](#overview)
2. [File Structure](#file-structure)
3. [Color Palette](#color-palette)
4. [Theme Implementation](#theme-implementation)
5. [Usage Examples](#usage-examples)
6. [Theme Switching](#theme-switching)
7. [Best Practices](#best-practices)

## Overview

The NewLekky app uses a comprehensive theming system that supports both light and dark modes. The theme is designed to be consistent across the app while providing visual differentiation between different screens (Home, History, Cost).

The theme implementation follows these principles:
- Consistent color usage across the app
- Screen-specific color accents
- Proper contrast for accessibility
- Smooth transitions between light and dark modes
- Extensible theme system for future enhancements

## File Structure

The theme system consists of the following files:

- `lib/core/theme/app_theme.dart` - Core theme definitions and colors
- `lib/core/theme/theme_manager.dart` - Theme state management and persistence
- `lib/core/theme/theme_usage_example.dart` - Examples of how to use the theme

Additional documentation:
- `color_palette.md` - Comprehensive color palette documentation
- `dialog_and_card_specifications.md` - Specifications for dialogs and cards

## Color Palette

The color palette is divided into several categories:

### Primary Colors

- **Primary Blue** - Used for the Home screen, buttons, and links
- **Primary Purple** - Used for the History screen
- **Primary Orange** - Used for the Cost screen
- **Accent Green** - Used for action buttons and success indicators

### Background Colors

- **Background** - Main app background
- **Surface** - Cards, dialogs, and elevated surfaces
- **Card Background** - List items and data cards
- **Header Background** - Table headers and section headers

### Text Colors

- **Text Primary** - Primary text content
- **Text Secondary** - Secondary text and labels
- **Text Tertiary** - Hints and disabled text
- **Text Value** - Numeric values and measurements
- **Text Currency** - Currency values

For a complete list of colors and their usage, see the `color_palette.md` file.

## Theme Implementation

The theme is implemented using Flutter's `ThemeData` class and custom theme extensions.

### Core Theme Configuration

The `AppTheme` class in `app_theme.dart` provides static methods to get the light and dark themes:

```dart
// Get light theme
ThemeData lightTheme = AppTheme.lightTheme;

// Get dark theme
ThemeData darkTheme = AppTheme.darkTheme;
```

### Custom Theme Extensions

The theme includes a custom extension called `LekkyColors` that provides additional colors specific to the NewLekky app:

```dart
// Access custom colors
final lekkyColors = Theme.of(context).lekkyColors;

// Use custom colors
Color homeAppBarColor = lekkyColors.homeAppBar;
Color textValueColor = lekkyColors.textValue;
```

### Screen-Specific Colors

The theme provides a helper method to get the appropriate app bar color based on the current screen:

```dart
// Get app bar color for a specific screen
Color appBarColor = Theme.of(context).getAppBarColor('history');
```

## Usage Examples

### Setting Up the Theme

In your `main.dart` file, set up the theme provider:

```dart
void main() {
  runApp(
    ProviderScope(
      child: Consumer(
        builder: (context, ref, _) {
          final theme = ref.watch(themeProvider);
          
          return MaterialApp(
            title: 'NewLekky',
            theme: theme,
            home: const HomeScreen(),
          );
        },
      ),
    ),
  );
}
```

### Using Theme Colors

When building UI components, use the theme colors instead of hardcoded values:

```dart
// Good - uses theme colors
Text(
  'Meter Reading',
  style: TextStyle(
    color: Theme.of(context).lekkyColors.textValue,
    fontWeight: FontWeight.bold,
  ),
)

// Bad - uses hardcoded colors
Text(
  'Meter Reading',
  style: TextStyle(
    color: const Color(0xFF1976D2),
    fontWeight: FontWeight.bold,
  ),
)
```

### Themed Components

The theme includes pre-configured styles for common components:

```dart
// Buttons
ElevatedButton(
  onPressed: () {},
  child: const Text('Save'),
)

// Text fields
TextField(
  decoration: const InputDecoration(
    labelText: 'Meter Reading',
    hintText: 'Enter meter reading',
  ),
)

// Cards
Card(
  child: Padding(
    padding: const EdgeInsets.all(16.0),
    child: Text('Card content'),
  ),
)
```

For more examples, see the `theme_usage_example.dart` file.

## Theme Switching

The app supports three theme modes:
- System - Follows the system theme
- Light - Always uses the light theme
- Dark - Always uses the dark theme

### Theme Mode Provider

The current theme mode is managed by a Riverpod provider:

```dart
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);
```

### Changing the Theme Mode

To change the theme mode:

```dart
// Set a specific theme mode
ref.read(themeModeProvider.notifier).setThemeMode(ThemeMode.dark);

// Toggle between light and dark
ref.read(themeModeProvider.notifier).toggleTheme();
```

### Theme Settings UI

The `ThemeSettings` widget provides a pre-built UI for changing the theme mode:

```dart
const ThemeSettings(),
```

## Best Practices

1. **Always use theme colors** - Never hardcode colors in your UI
2. **Use semantic color names** - Use `primaryColor` instead of `blue`
3. **Test both themes** - Ensure your UI looks good in both light and dark modes
4. **Follow the color usage guidelines** - Use colors consistently across the app
5. **Use the provided components** - Use the themed components instead of creating custom ones
6. **Consider accessibility** - Ensure sufficient contrast for text and interactive elements

## Conclusion

By following this guide, you can ensure that your UI is consistent with the rest of the app and provides a great user experience in both light and dark modes.

For more detailed information, refer to the `color_palette.md` and `dialog_and_card_specifications.md` files.
