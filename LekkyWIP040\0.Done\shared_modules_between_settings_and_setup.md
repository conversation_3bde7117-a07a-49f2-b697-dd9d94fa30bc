# Shared Modules Between Settings and Setup Screens

This document outlines the shared modules, components, and functionality between the Settings and Setup screens in the NewLekky app.

## 1. Shared UI Components

### 1.1 Theme Selection Component
- **Location**: 
  - Setup: `features/setup/presentation/widgets/appearance_settings_card.dart`
  - Settings: Should use the same component
- **Functionality**: Allows users to select between System Default, Light Mode, and Dark Mode
- **Shared Elements**:
  - Radio button UI with icons
  - Theme mode selection logic
  - Theme persistence

### 1.2 Notification Toggle Components
- **Location**: 
  - Setup: `features/setup/presentation/widgets/notifications_settings_card.dart`
  - Settings: Should use the same component
- **Functionality**: Enables/disables various notification types
- **Shared Elements**:
  - Toggle switch UI
  - Notification type descriptions
  - Permission information note

### 1.3 Reminder Frequency Selection
- **Location**: 
  - Setup: `features/setup/presentation/widgets/reminders_settings_card.dart`
  - Settings: Should use the same component
- **Functionality**: Allows selection of reminder frequency and time
- **Shared Elements**:
  - Frequency option grid (Daily, Weekly, Bi-weekly, Monthly)
  - Time picker UI
  - Reminder status indicator

## 2. Shared Models

### 2.1 Theme Mode Model
- **Location**: 
  - Setup: `features/setup/domain/models/setup_preferences.dart` (AppThemeMode enum)
  - Settings: Should use the same model
- **Functionality**: Represents the theme mode options
- **Shared Elements**:
  - Enum values (system, light, dark)
  - Parsing and serialization logic

### 2.2 Reminder Frequency Model
- **Location**: 
  - Setup: `features/setup/domain/models/setup_preferences.dart` (ReminderFrequency enum)
  - Settings: Should use the same model
- **Functionality**: Represents the reminder frequency options
- **Shared Elements**:
  - Enum values (daily, weekly, biWeekly, monthly)
  - Parsing and serialization logic

## 3. Shared Business Logic

### 3.1 Theme Management
- **Location**: 
  - Setup: `features/setup/presentation/controllers/setup_controller.dart` (setThemeMode method)
  - Settings: Should use the same logic
- **Functionality**: Manages theme mode changes
- **Shared Elements**:
  - Theme mode setting logic
  - Theme persistence

### 3.2 Notification Management
- **Location**: 
  - Setup: `features/setup/presentation/controllers/setup_controller.dart` (notification methods)
  - Settings: Should use the same logic
- **Functionality**: Manages notification preferences
- **Shared Elements**:
  - Notification toggle logic
  - Notification persistence

### 3.3 Reminder Management
- **Location**: 
  - Setup: `features/setup/presentation/controllers/setup_controller.dart` (reminder methods)
  - Settings: Should use the same logic
- **Functionality**: Manages reminder preferences
- **Shared Elements**:
  - Reminder frequency setting logic
  - Reminder time setting logic
  - Reminder persistence

## 4. Shared Persistence

### 4.1 SharedPreferences Keys
- **Location**: 
  - Setup: `features/setup/domain/models/setup_preferences.dart` (saveToPreferences and loadFromPreferences methods)
  - Settings: Should use the same keys
- **Functionality**: Stores user preferences
- **Shared Elements**:
  - Preference key names
  - Serialization/deserialization logic

## 5. Refactoring Recommendations

To improve code reuse and maintainability, the following refactoring is recommended:

1. **Extract Shared UI Components**:
   - Move shared UI components to a common location (e.g., `core/widgets/`)
   - Create reusable components like `ThemeSelector`, `NotificationToggle`, and `ReminderFrequencySelector`

2. **Extract Shared Models**:
   - Move shared enums and models to a common location (e.g., `core/models/`)
   - Create shared models like `ThemeMode` and `ReminderFrequency`

3. **Extract Shared Business Logic**:
   - Create a shared service for theme management (e.g., `core/services/theme_service.dart`)
   - Create a shared service for notification management (e.g., `core/services/notification_service.dart`)
   - Create a shared service for reminder management (e.g., `core/services/reminder_service.dart`)

4. **Standardize Persistence**:
   - Create a shared constants file for preference keys (e.g., `core/constants/preference_keys.dart`)
   - Create a shared service for preference management (e.g., `core/services/preference_service.dart`)

By implementing these refactoring recommendations, the codebase will be more maintainable, with less duplication and better separation of concerns.
