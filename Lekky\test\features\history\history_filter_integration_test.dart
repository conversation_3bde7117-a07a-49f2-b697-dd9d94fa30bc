import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/shared/enums/entry_enums.dart';
import 'package:lekky/features/history/domain/models/history_state.dart';

/// Integration test for History filter functionality
/// This test verifies that the filter state management works correctly
void main() {
  group('History Filter Integration', () {
    test('HistoryState should handle filter combinations correctly', () {
      // Test default state
      const defaultState = HistoryState();
      expect(defaultState.filterType, EntryFilterType.all);
      expect(defaultState.sortOrder, EntrySortOrder.newestFirst);
      expect(defaultState.startDate, isNull);
      expect(defaultState.endDate, isNull);

      // Test state with all filters applied
      final filteredState = defaultState.copyWith(
        filterType: EntryFilterType.meterReadings,
        sortOrder: EntrySortOrder.oldestFirst,
        startDate: DateTime(2024, 1, 1),
        endDate: DateTime(2024, 12, 31),
      );

      expect(filteredState.filterType, EntryFilterType.meterReadings);
      expect(filteredState.sortOrder, EntrySortOrder.oldestFirst);
      expect(filteredState.startDate, DateTime(2024, 1, 1));
      expect(filteredState.endDate, DateTime(2024, 12, 31));
      expect(filteredState.hasDateFilter, isTrue);
    });

    test('HistoryState extensions should work correctly', () {
      // Test hasDateFilter extension
      const stateWithoutDate = HistoryState();
      expect(stateWithoutDate.hasDateFilter, isFalse);

      final stateWithPartialDate = HistoryState(
        startDate: DateTime(2024, 1, 1),
        endDate: null,
      );
      expect(stateWithPartialDate.hasDateFilter, isFalse);

      final stateWithFullDate = HistoryState(
        startDate: DateTime(2024, 1, 1),
        endDate: DateTime(2024, 12, 31),
      );
      expect(stateWithFullDate.hasDateFilter, isTrue);

      // Test pagination extensions
      const stateWithPagination = HistoryState(
        currentPage: 2,
        totalPages: 5,
      );
      expect(stateWithPagination.canGoNext, isTrue);
      expect(stateWithPagination.canGoPrevious, isTrue);
      expect(stateWithPagination.displayPage, 3); // 1-based display
      expect(stateWithPagination.needsPagination, isTrue);

      const stateFirstPage = HistoryState(
        currentPage: 0,
        totalPages: 3,
      );
      expect(stateFirstPage.canGoPrevious, isFalse);
      expect(stateFirstPage.canGoNext, isTrue);

      const stateLastPage = HistoryState(
        currentPage: 2,
        totalPages: 3,
      );
      expect(stateLastPage.canGoNext, isFalse);
      expect(stateLastPage.canGoPrevious, isTrue);
    });

    test('Filter combinations should be preserved in state', () {
      // Start with default state
      const initialState = HistoryState();

      // Apply entry type filter
      final step1 = initialState.copyWith(
        filterType: EntryFilterType.topUps,
      );
      expect(step1.filterType, EntryFilterType.topUps);
      expect(step1.sortOrder,
          EntrySortOrder.newestFirst); // Should preserve default

      // Apply sort order filter
      final step2 = step1.copyWith(
        sortOrder: EntrySortOrder.oldestFirst,
      );
      expect(
          step2.filterType, EntryFilterType.topUps); // Should preserve previous
      expect(step2.sortOrder, EntrySortOrder.oldestFirst);

      // Apply date range filter
      final step3 = step2.copyWith(
        startDate: DateTime(2024, 6, 1),
        endDate: DateTime(2024, 6, 30),
      );
      expect(step3.filterType, EntryFilterType.topUps); // Should preserve
      expect(step3.sortOrder, EntrySortOrder.oldestFirst); // Should preserve
      expect(step3.startDate, DateTime(2024, 6, 1));
      expect(step3.endDate, DateTime(2024, 6, 30));
      expect(step3.hasDateFilter, isTrue);

      // Reset to defaults
      final reset = step3.copyWith(
        filterType: EntryFilterType.all,
        sortOrder: EntrySortOrder.newestFirst,
        startDate: null,
        endDate: null,
      );
      expect(reset.filterType, EntryFilterType.all);
      expect(reset.sortOrder, EntrySortOrder.newestFirst);
      expect(reset.startDate, isNull);
      expect(reset.endDate, isNull);
      expect(reset.hasDateFilter, isFalse);
    });

    test('Entry filter types should have correct values', () {
      expect(EntryFilterType.values.length, 4);
      expect(EntryFilterType.values, contains(EntryFilterType.all));
      expect(EntryFilterType.values, contains(EntryFilterType.meterReadings));
      expect(EntryFilterType.values, contains(EntryFilterType.topUps));
      expect(EntryFilterType.values, contains(EntryFilterType.invalid));
    });

    test('Sort order types should have correct values', () {
      expect(EntrySortOrder.values.length, 2);
      expect(EntrySortOrder.values, contains(EntrySortOrder.newestFirst));
      expect(EntrySortOrder.values, contains(EntrySortOrder.oldestFirst));
    });

    test('State should handle edge cases correctly', () {
      // Test with same start and end date
      final sameDate = DateTime(2024, 6, 15);
      final stateSameDate = HistoryState(
        startDate: sameDate,
        endDate: sameDate,
      );
      expect(stateSameDate.hasDateFilter, isTrue);

      // Test with zero entries
      const stateEmpty = HistoryState(
        entries: [],
        totalEntries: 0,
        totalPages: 1,
      );
      expect(stateEmpty.hasEntries, isFalse);
      expect(stateEmpty.needsPagination, isFalse);

      // Test with single page
      const stateSinglePage = HistoryState(
        totalPages: 1,
        currentPage: 0,
      );
      expect(stateSinglePage.canGoNext, isFalse);
      expect(stateSinglePage.canGoPrevious, isFalse);
      expect(stateSinglePage.needsPagination, isFalse);
    });

    test('Sort order should be preserved with entry type filters', () {
      // Test that sort order is maintained when combined with entry type filters
      const stateWithMeterReadingsOldestFirst = HistoryState(
        filterType: EntryFilterType.meterReadings,
        sortOrder: EntrySortOrder.oldestFirst,
      );
      expect(stateWithMeterReadingsOldestFirst.filterType,
          EntryFilterType.meterReadings);
      expect(stateWithMeterReadingsOldestFirst.sortOrder,
          EntrySortOrder.oldestFirst);
      expect(stateWithMeterReadingsOldestFirst.hasDateFilter, isFalse);

      const stateWithTopUpsOldestFirst = HistoryState(
        filterType: EntryFilterType.topUps,
        sortOrder: EntrySortOrder.oldestFirst,
      );
      expect(stateWithTopUpsOldestFirst.filterType, EntryFilterType.topUps);
      expect(stateWithTopUpsOldestFirst.sortOrder, EntrySortOrder.oldestFirst);
      expect(stateWithTopUpsOldestFirst.hasDateFilter, isFalse);

      const stateWithInvalidOldestFirst = HistoryState(
        filterType: EntryFilterType.invalid,
        sortOrder: EntrySortOrder.oldestFirst,
      );
      expect(stateWithInvalidOldestFirst.filterType, EntryFilterType.invalid);
      expect(stateWithInvalidOldestFirst.sortOrder, EntrySortOrder.oldestFirst);
      expect(stateWithInvalidOldestFirst.hasDateFilter, isFalse);
    });
  });
}
