# UI Components for NewLekky App

This document defines the standardized UI components to be used throughout the NewLekky app, ensuring visual consistency and reusability.

## Core Components

### Buttons

#### Primary Button

- **Usage**: Main actions (Save, Add, Confirm)
- **Appearance**: 
  - Blue background (#007AFF)
  - White text
  - Rounded corners (8dp radius)
  - Elevation shadow (2dp)
- **States**:
  - Normal: Full opacity
  - Pressed: Darker shade (#0062CC)
  - Disabled: 50% opacity

#### Secondary Button

- **Usage**: Alternative actions (Cancel, Back)
- **Appearance**:
  - White background
  - Blue text (#007AFF)
  - Blue border (1dp)
  - Rounded corners (8dp radius)
- **States**:
  - Normal: Full opacity
  - Pressed: Light blue background (#E6F2FF)
  - Disabled: 50% opacity

#### Danger Button

- **Usage**: Destructive actions (Delete)
- **Appearance**:
  - Red background (#FF3B30)
  - White text
  - Rounded corners (8dp radius)
- **States**:
  - Normal: Full opacity
  - Pressed: Darker red (#CC2F26)
  - Disabled: 50% opacity

### Cards

#### Entry Card

- **Usage**: Display meter readings or top-ups in lists
- **Appearance**:
  - White background
  - Rounded corners (12dp radius)
  - Elevation shadow (4dp)
  - Padding: 16dp
- **Content**:
  - Date/time (top-left)
  - Value/amount (top-right)
  - Type indicator (icon, left side)
  - Validity indicator (if applicable)
- **States**:
  - Normal: White background
  - Selected: Light blue background (#E6F2FF)
  - Invalid: Light red border or indicator

#### Meter Status Card

- **Usage**: Display current meter status on home screen
- **Appearance**:
  - Gradient background (blue to teal)
  - Rounded corners (16dp radius)
  - Elevation shadow (8dp)
  - Padding: 24dp
- **Content**:
  - Current reading (large text)
  - Last updated date
  - Days remaining indicator
  - Quick action buttons

#### Summary Card

- **Usage**: Display summary information and statistics
- **Appearance**:
  - White background
  - Rounded corners (12dp radius)
  - Elevation shadow (4dp)
  - Padding: 16dp
- **Content**:
  - Title (top)
  - Value (center, large text)
  - Subtitle or description (bottom)
  - Optional icon or indicator

### Input Fields

#### Text Input

- **Usage**: Text and numeric input
- **Appearance**:
  - Outlined border (1dp)
  - Rounded corners (8dp radius)
  - Padding: 16dp horizontal, 12dp vertical
  - Label text above input
- **States**:
  - Normal: Gray border (#CCCCCC)
  - Focused: Blue border (#007AFF)
  - Error: Red border (#FF3B30)
  - Disabled: Light gray background (#F2F2F7)
- **Validation**:
  - Error text below input
  - Error icon at right side

#### Date/Time Picker

- **Usage**: Select date and time
- **Appearance**:
  - Same as Text Input
  - Calendar icon at right side
- **Behavior**:
  - Tap to open native date/time picker
  - Format display based on user's locale

#### Segmented Control

- **Usage**: Switch between related options
- **Appearance**:
  - Rounded rectangle container
  - Equal-width segments
  - 1dp dividers between segments
- **States**:
  - Selected: Blue background (#007AFF), white text
  - Unselected: White background, black text

### Dialogs

#### Add Entry Dialog

- **Usage**: Create new meter reading or top-up
- **Appearance**:
  - White background
  - Rounded corners (16dp radius)
  - Title with blue plus icon
- **Content**:
  - Entry type selector (segmented control)
  - Date/time picker
  - Value/amount input
  - Cancel and Save buttons

#### Edit Entry Dialog

- **Usage**: Modify existing entry
- **Appearance**:
  - White background
  - Rounded corners (16dp radius)
  - Title with blue pencil icon
- **Content**:
  - Entry type selector (segmented control)
  - Date/time picker
  - Value/amount input
  - Delete, Cancel, and Save buttons

#### Delete Confirmation Dialog

- **Usage**: Confirm entry deletion
- **Appearance**:
  - White background
  - Rounded corners (16dp radius)
  - Title with red trash icon
- **Content**:
  - Warning message
  - Cancel and Delete buttons

### Navigation

#### Bottom Navigation Bar

- **Usage**: Main app navigation
- **Appearance**:
  - White background
  - Top separator line (1dp)
  - Equal-width items
- **Items**:
  - Home (house icon)
  - Cost (pound/£ icon)
  - History (clock/history icon)
- **States**:
  - Selected: Blue icon and text
  - Unselected: Gray icon and text

#### App Bar

- **Usage**: Screen title and actions
- **Appearance**:
  - White or themed background
  - Bottom separator line (1dp)
  - Elevation shadow (2dp)
- **Content**:
  - Screen title (center)
  - Back button (if applicable, left)
  - Action buttons (right)

### Feedback Components

#### Message Bar

- **Usage**: Display notifications and alerts
- **Appearance**:
  - Colored background based on type
  - Rounded corners (8dp radius)
  - Padding: 16dp
- **Types**:
  - Info: Blue background (#007AFF)
  - Success: Green background (#34C759)
  - Warning: Yellow background (#FFCC00)
  - Error: Red background (#FF3B30)
- **Content**:
  - Icon based on type
  - Message text
  - Optional action button
  - Dismiss button or auto-dismiss

#### Progress Indicators

- **Usage**: Show loading or progress
- **Types**:
  - Circular: For general loading
  - Linear: For determinate progress
- **Appearance**:
  - Blue color (#007AFF)
  - Appropriate size based on context

#### Empty State

- **Usage**: Display when no data is available
- **Appearance**:
  - Centered content
  - Illustration or icon
  - Message text
  - Optional action button

## Visualization Components

### Line Chart

- **Usage**: Display usage trends over time
- **Appearance**:
  - Grid lines (light gray)
  - X-axis: Time periods
  - Y-axis: Usage values
  - Blue line for data points
  - Data point markers
- **Interaction**:
  - Tap on point to see details
  - Pinch to zoom
  - Pan to scroll

### Bar Chart

- **Usage**: Display cost breakdowns
- **Appearance**:
  - Grid lines (light gray)
  - X-axis: Time periods
  - Y-axis: Cost values
  - Blue bars for data points
- **Interaction**:
  - Tap on bar to see details
  - Toggle between time periods

## Accessibility Considerations

- All components support dynamic text sizing
- Color contrast meets WCAG 2.1 AA standards
- Interactive elements have minimum touch target size (48x48dp)
- All components include appropriate semantic labels
- Animations can be disabled for reduced motion preference

## Implementation Guidelines

1. Create a base component library in `lib/shared/widgets/`
2. Use composition for complex components
3. Implement theming support for light/dark mode
4. Create widget tests for all components
5. Document usage examples for each component
6. Ensure responsive behavior on different screen sizes
