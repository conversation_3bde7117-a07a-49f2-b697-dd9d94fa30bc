targets:
  $default:
    builders:
      riverpod_generator:
        options:
          # Generate providers with proper naming
          provider_name_prefix: ""
          provider_name_suffix: "Provider"
      freezed:
        options:
          # Generate copyWith and toString methods
          copy_with: true
          to_string: true
          # Generate equality and hashCode
          equal: true
          # Generate when and map methods for unions
          when: true
          map: true
