# NewLekky Color Palette Guide

This document provides a comprehensive guide to the color palette for the NewLekky app, including both light and dark mode colors, usage guidelines, and component-specific adaptations based on the provided screenshots.

## Core Color Palette

### Primary Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| Primary Blue | #1976D2 | #2196F3 | App bar (Home), buttons, links, active indicators |
| Primary Purple | #9C27B0 | #BB86FC | App bar (History), active indicators |
| Primary Orange | #FF5722 | #FF7043 | App bar (Cost), active indicators |
| Accent Green | #4CAF50 | #00E676 | Action buttons, success indicators |
| Warning Yellow | #FFC107 | #FFAB00 | Warning indicators, alerts |
| Error Red | #F44336 | #CF6679 | Error indicators, delete actions |

### Background Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| Background | #F5F5F5 | #121212 | Main app background |
| Surface | #FFFFFF | #1E1E1E | Cards, dialogs, elevated surfaces |
| Card Background | #FFFFFF | #2C2C2C | List items, data cards |
| Header Background | #EEEEEE | #333333 | Table headers, section headers |
| Divider | #DDDDDD | #424242 | Separators, dividers |
| Bottom Navigation | #FFFFFF | #1E1E1E | Bottom navigation bar |
| Status Bar | Primary color | Primary color | Status bar background |

### Text Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| Text Primary | #212121 | #FFFFFF | Primary text content |
| Text Secondary | #757575 | #B3FFFFFF (70% white) | Secondary text, labels |
| Text Tertiary | #9E9E9E | #70FFFFFF (45% white) | Hints, disabled text |
| Text On Primary | #FFFFFF | #FFFFFF | Text on primary colored backgrounds |
| Text Link | #1976D2 | #64B5F6 | Interactive text links |
| Text Value | #1976D2 | #64B5F6 | Numeric values, measurements |
| Text Currency | #1976D2 | #64B5F6 | Currency values |

### Border and Icon Colors

| Color Name | Light Mode | Dark Mode | Usage |
|------------|------------|-----------|-------|
| Border | #DDDDDD | #424242 | Standard borders |
| Border Focus | #1976D2 | #2196F3 | Focused input borders |
| Border Error | #F44336 | #CF6679 | Error state borders |
| Icon Primary | #212121 | #FFFFFF | Primary icons |
| Icon Secondary | #757575 | #B3FFFFFF | Secondary icons |
| Icon On Primary | #FFFFFF | #FFFFFF | Icons on primary colored backgrounds |
| Icon Active | Primary color | Primary color | Active state icons |

## Component-Specific Colors

### App Bars

| Screen | Light Mode | Dark Mode | Text/Icon Color |
|--------|------------|-----------|----------------|
| Home | #1976D2 | #0D47A1 | #FFFFFF |
| History | #9C27B0 | #7B1FA2 | #FFFFFF |
| Cost | #FF5722 | #E64A19 | #FFFFFF |

### Cards

| Card Type | Background (Light) | Background (Dark) | Border (Light) | Border (Dark) |
|-----------|-------------------|-------------------|----------------|---------------|
| Standard Card | #FFFFFF | #2C2C2C | #DDDDDD | #424242 |
| Highlighted Card | #FFFFFF | #2C2C2C | Primary color | Primary color |
| Warning Card | #FFF8E1 | #3E2F00 | #FFECB3 | #FFC107 |
| Error Card | #FFEBEE | #3E0000 | #FFCDD2 | #F44336 |

### List Items

| State | Background (Light) | Background (Dark) | Text (Light) | Text (Dark) |
|-------|-------------------|-------------------|--------------|-------------|
| Normal | #FFFFFF | #2C2C2C | #212121 | #FFFFFF |
| Selected | #E3F2FD | #0D47A1 | #1976D2 | #FFFFFF |
| Warning | #FFFDE7 | #3E3500 | #212121 | #FFFFFF |
| Error | #FFEBEE | #3E0000 | #212121 | #FFFFFF |
| Disabled | #F5F5F5 | #1E1E1E | #9E9E9E | #70FFFFFF |

### Buttons

| Button Type | Background (Light) | Background (Dark) | Text (Light) | Text (Dark) |
|-------------|-------------------|-------------------|--------------|-------------|
| Primary | #1976D2 | #2196F3 | #FFFFFF | #FFFFFF |
| Secondary | #FFFFFF | #424242 | #1976D2 | #2196F3 |
| Danger | #F44336 | #CF6679 | #FFFFFF | #FFFFFF |
| Success | #4CAF50 | #00E676 | #FFFFFF | #000000 |
| Disabled | #EEEEEE | #424242 | #9E9E9E | #70FFFFFF |

### Bottom Navigation

| State | Icon/Text (Light) | Icon/Text (Dark) | Background (Light) | Background (Dark) |
|-------|-------------------|------------------|-------------------|-------------------|
| Active | Primary color | Primary color | #FFFFFF | #1E1E1E |
| Inactive | #757575 | #B3FFFFFF | #FFFFFF | #1E1E1E |

### Input Fields

| State | Border (Light) | Border (Dark) | Background (Light) | Background (Dark) |
|-------|---------------|---------------|-------------------|-------------------|
| Normal | #DDDDDD | #424242 | #FFFFFF | #2C2C2C |
| Focused | #1976D2 | #2196F3 | #FFFFFF | #2C2C2C |
| Error | #F44336 | #CF6679 | #FFFFFF | #2C2C2C |
| Disabled | #EEEEEE | #333333 | #F5F5F5 | #1E1E1E |

## Screen-Specific Color Usage

### Home Screen

- **App Bar**: Primary Blue
- **Meter Total Card**: Dark surface (#2C2C2C) with blue text (#64B5F6)
- **Status Card**: Dark surface (#2C2C2C) with blue text (#64B5F6)
- **Add Entry Button**: Accent Green (#00E676) with white text
- **Bottom Navigation**: Dark surface (#1E1E1E) with active item in green

### History Screen

- **App Bar**: Primary Purple
- **Table Header**: Dark header (#333333) with blue text (#64B5F6)
- **List Items**: Dark surface (#2C2C2C) with white text and blue values
- **Warning Indicators**: Yellow triangle icons
- **Pagination Controls**: Dark background with blue navigation controls
- **Bottom Navigation**: Dark surface with active item in purple

### Cost Screen

- **App Bar**: Primary Orange
- **Cost Card**: Dark surface (#2C2C2C) with blue text for values
- **Time Period Selector**: Blue active selection (#2196F3)
- **Date Pickers**: Dark surface (#2C2C2C) with white text
- **Custom Button**: Blue outline (#2196F3) with blue text
- **Bottom Navigation**: Dark surface with active item in orange

## Color Usage Guidelines

### Text Readability

- Maintain minimum contrast ratio of 4.5:1 for normal text and 3:1 for large text
- Use Text Primary on light backgrounds and surfaces
- Use Text On Primary on colored backgrounds (app bars, buttons)
- Use Text Secondary for supporting information

### Status and Feedback Colors

- **Success/Positive**: Accent Green (#00E676)
- **Warning/Caution**: Warning Yellow (#FFAB00)
- **Error/Negative**: Error Red (#CF6679)
- **Information**: Primary Blue (#2196F3)

### Color Combinations to Avoid

- Purple text on blue backgrounds (poor contrast)
- Yellow text on white backgrounds (poor contrast)
- Green text on red backgrounds (color blindness issues)

## Dark Mode Adaptation Guidelines

1. **Invert Luminosity, Not Hue**:
   - Keep the same hue for brand colors
   - Invert luminosity for backgrounds and surfaces
   - Adjust saturation for better visibility in dark environments

2. **Increase Contrast for Text**:
   - Use pure white (#FFFFFF) for primary text
   - Use 70% white (#B3FFFFFF) for secondary text
   - Use 45% white (#70FFFFFF) for tertiary/disabled text

3. **Reduce Brightness of Large Colored Areas**:
   - App bars should be darker versions of their light mode counterparts
   - Large buttons should be less saturated in dark mode

4. **Elevation Differentiation**:
   - Higher elevation surfaces should be slightly lighter
   - Use the formula: Surface color + (8% × Elevation)

## Inconsistencies and Areas for Clarification

1. **Notification Badge Colors**:
   - The red notification badge with white text appears in both light and dark mode
   - Need to confirm if this is intentional for maximum visibility

2. **Warning Indicator Consistency**:
   - Yellow triangle warnings appear in some list items
   - Need to clarify the exact criteria for showing these indicators

3. **Currency Value Color Consistency**:
   - Some currency values use orange (#FF7043) while others use blue (#64B5F6)
   - Need to standardize or clarify when each color should be used

4. **Table Header Background**:
   - Some tables use a darker header row while others don't
   - Need to clarify when to use differentiated headers

5. **Card Border Usage**:
   - Some cards have visible borders (yellow) while others don't
   - Need to clarify when borders should be used on cards

## Implementation Notes

1. **Theme Implementation**:
   - Use Flutter's ThemeData with light and dark variants
   - Create extension methods for custom colors not covered by MaterialColor

2. **Color Opacity Handling**:
   - Use Color.withOpacity() for consistent transparency effects
   - Define standard opacity levels (e.g., 0.7 for disabled, 0.5 for overlays)

3. **Accessibility Considerations**:
   - Test all color combinations with accessibility tools
   - Provide high contrast mode option for additional accessibility

4. **Animation Between Themes**:
   - Implement smooth crossfade animation (300ms duration)
   - Use AnimatedTheme for smooth transitions between light and dark mode
