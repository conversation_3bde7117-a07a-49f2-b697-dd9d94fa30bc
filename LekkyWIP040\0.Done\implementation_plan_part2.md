# Implementation Plan for NewLekky Splash and Welcome Screens (Part 2)

### 4. Welcome Screen Implementation

1. Create feature item widget
   ```dart
   // feature_item.dart
   import 'package:flutter/material.dart';
   import '../../../core/constants/app_colors.dart';

   class FeatureItem extends StatelessWidget {
     final IconData icon;
     final String title;
     final String description;

     const FeatureItem({
       Key? key,
       required this.icon,
       required this.title,
       required this.description,
     }) : super(key: key);

     @override
     Widget build(BuildContext context) {
       return Row(
         crossAxisAlignment: CrossAxisAlignment.start,
         children: [
           Container(
             width: 48,
             height: 48,
             decoration: BoxDecoration(
               color: AppColors.secondaryBlue,
               borderRadius: BorderRadius.circular(12),
             ),
             child: Icon(
               icon,
               color: Colors.white,
               size: 24,
             ),
           ),
           const SizedBox(width: 16),
           Expanded(
             child: Column(
               crossAxisAlignment: CrossAxisAlignment.start,
               children: [
                 Text(
                   title,
                   style: Theme.of(context).textTheme.headline3,
                 ),
                 const SizedBox(height: 4),
                 Text(
                   description,
                   style: Theme.of(context).textTheme.bodyText2,
                 ),
               ],
             ),
           ),
         ],
       );
     }
   }
   ```

2. Create welcome controller
   ```dart
   // welcome_controller.dart
   import 'package:flutter/material.dart';
   import '../../../core/utils/app_initializer.dart';

   class WelcomeController extends ChangeNotifier {
     Future<void> markWelcomeCompleted() async {
       await AppInitializer.markWelcomeCompleted();
     }
     
     Future<void> restoreData() async {
       // Implement data restoration logic
       await Future.delayed(Duration(seconds: 2)); // Simulate restoration
       return;
     }
   }
   ```

3. Create welcome screen
   ```dart
   // welcome_screen.dart
   import 'package:flutter/material.dart';
   import 'package:provider/provider.dart';
   import '../controllers/welcome_controller.dart';
   import '../widgets/feature_item.dart';
   import '../../../core/constants/app_colors.dart';
   import '../../../core/constants/app_strings.dart';
   import '../../../setup/presentation/screens/setup_screen.dart';

   class WelcomeScreen extends StatelessWidget {
     @override
     Widget build(BuildContext context) {
       return Scaffold(
         backgroundColor: AppColors.primaryBlue,
         body: SafeArea(
           child: Column(
             children: [
               Expanded(
                 child: SingleChildScrollView(
                   child: Padding(
                     padding: const EdgeInsets.symmetric(horizontal: 24.0),
                     child: Column(
                       crossAxisAlignment: CrossAxisAlignment.center,
                       children: [
                         const SizedBox(height: 40),
                         // Logo
                         Image.asset(
                           'assets/images/lekky_logo.png',
                           width: 80,
                           height: 80,
                         ),
                         const SizedBox(height: 16),
                         // Welcome Text
                         Text(
                           AppStrings.welcomeTitle,
                           style: Theme.of(context).textTheme.headline2,
                           textAlign: TextAlign.center,
                         ),
                         const SizedBox(height: 8),
                         // Subtitle
                         Text(
                           AppStrings.welcomeSubtitle,
                           style: Theme.of(context).textTheme.bodyText1,
                           textAlign: TextAlign.center,
                         ),
                         const SizedBox(height: 40),
                         // Features
                         FeatureItem(
                           icon: Icons.track_changes,
                           title: AppStrings.trackUsage,
                           description: AppStrings.trackUsageDesc,
                         ),
                         const SizedBox(height: 24),
                         FeatureItem(
                           icon: Icons.notifications,
                           title: AppStrings.getAlerts,
                           description: AppStrings.getAlertsDesc,
                         ),
                         const SizedBox(height: 24),
                         FeatureItem(
                           icon: Icons.history,
                           title: AppStrings.viewHistory,
                           description: AppStrings.viewHistoryDesc,
                         ),
                         const SizedBox(height: 24),
                         FeatureItem(
                           icon: Icons.attach_money,
                           title: AppStrings.calculateCosts,
                           description: AppStrings.calculateCostsDesc,
                         ),
                         const SizedBox(height: 40),
                         // Restore Data Button
                         OutlinedButton.icon(
                           onPressed: () => _showRestoreOptions(context),
                           icon: Icon(Icons.restore, color: Colors.white),
                           label: Text(AppStrings.restoreData),
                           style: OutlinedButton.styleFrom(
                             minimumSize: Size(MediaQuery.of(context).size.width * 0.8, 0),
                           ),
                         ),
                         const SizedBox(height: 8),
                         Text(
                           AppStrings.restoreHelper,
                           style: TextStyle(
                             color: Colors.white.withOpacity(0.8),
                             fontSize: 12,
                           ),
                         ),
                         const SizedBox(height: 24),
                       ],
                     ),
                   ),
                 ),
               ),
               // Get Started Button
               Padding(
                 padding: const EdgeInsets.all(24.0),
                 child: ElevatedButton.icon(
                   onPressed: () => _onGetStarted(context),
                   icon: Text(AppStrings.getStarted),
                   label: Icon(Icons.arrow_forward),
                   style: ElevatedButton.styleFrom(
                     minimumSize: Size(MediaQuery.of(context).size.width * 0.8, 0),
                   ),
                 ),
               ),
             ],
           ),
         ),
       );
     }

     void _showRestoreOptions(BuildContext context) {
       // Show restore options dialog or navigate to restore screen
       showDialog(
         context: context,
         builder: (context) => AlertDialog(
           title: Text('Restore Data'),
           content: Text('This feature will allow you to restore data from a backup file.'),
           actions: [
             TextButton(
               onPressed: () => Navigator.pop(context),
               child: Text('Cancel'),
             ),
             TextButton(
               onPressed: () {
                 Navigator.pop(context);
                 // Implement restore functionality
               },
               child: Text('Choose File'),
             ),
           ],
         ),
       );
     }

     void _onGetStarted(BuildContext context) {
       // Mark welcome as completed
       final welcomeController = Provider.of<WelcomeController>(context, listen: false);
       welcomeController.markWelcomeCompleted();
       
       // Navigate to setup screen
       Navigator.of(context).pushReplacement(
         MaterialPageRoute(builder: (_) => SetupScreen()),
       );
     }
   }
   ```

### 5. Main App Setup

1. Create app.dart
   ```dart
   // app.dart
   import 'package:flutter/material.dart';
   import 'package:provider/provider.dart';
   import 'core/theme/app_theme.dart';
   import 'features/splash/presentation/screens/splash_screen.dart';
   import 'features/splash/presentation/controllers/splash_controller.dart';
   import 'features/welcome/presentation/controllers/welcome_controller.dart';

   class LekkyApp extends StatelessWidget {
     @override
     Widget build(BuildContext context) {
       return MultiProvider(
         providers: [
           ChangeNotifierProvider(create: (_) => SplashController()),
           ChangeNotifierProvider(create: (_) => WelcomeController()),
         ],
         child: MaterialApp(
           title: 'Lekky',
           theme: AppTheme.lightTheme,
           darkTheme: AppTheme.darkTheme,
           themeMode: ThemeMode.system,
           home: SplashScreen(),
           debugShowCheckedModeBanner: false,
         ),
       );
     }
   }
   ```

2. Create main.dart
   ```dart
   // main.dart
   import 'package:flutter/material.dart';
   import 'package:flutter/services.dart';
   import 'app.dart';

   void main() {
     WidgetsFlutterBinding.ensureInitialized();
     
     // Set preferred orientations
     SystemChrome.setPreferredOrientations([
       DeviceOrientation.portraitUp,
       DeviceOrientation.portraitDown,
     ]);
     
     // Set system UI overlay style
     SystemChrome.setSystemUIOverlayStyle(
       SystemUiOverlayStyle(
         statusBarColor: Colors.transparent,
         statusBarIconBrightness: Brightness.light,
       ),
     );
     
     runApp(LekkyApp());
   }
   ```

### 6. Asset Configuration

1. Update pubspec.yaml
   ```yaml
   name: new_lekky
   description: A prepaid electricity meter tracking app.

   publish_to: 'none'
   version: 1.0.0+1

   environment:
     sdk: ">=2.12.0 <3.0.0"

   dependencies:
     flutter:
       sdk: flutter
     cupertino_icons: ^1.0.2
     provider: ^6.0.0
     shared_preferences: ^2.0.7

   dev_dependencies:
     flutter_test:
       sdk: flutter
     flutter_lints: ^1.0.0

   flutter:
     uses-material-design: true
     assets:
       - assets/images/
     fonts:
       - family: Roboto
         fonts:
           - asset: assets/fonts/Roboto-Regular.ttf
           - asset: assets/fonts/Roboto-Bold.ttf
             weight: 700
           - asset: assets/fonts/Roboto-Light.ttf
             weight: 300
   ```

## Testing Strategy

### 1. Unit Tests

1. Test app initialization
   ```dart
   // app_initializer_test.dart
   import 'package:flutter_test/flutter_test.dart';
   import 'package:shared_preferences/shared_preferences.dart';
   import 'package:new_lekky/core/utils/app_initializer.dart';

   void main() {
     TestWidgetsFlutterBinding.ensureInitialized();
     
     group('AppInitializer', () {
       test('initialize should complete successfully', () async {
         final result = await AppInitializer.initialize();
         expect(result, true);
       });
       
       test('isFirstTimeUser should return true for new users', () async {
         SharedPreferences.setMockInitialValues({});
         final result = await AppInitializer.isFirstTimeUser();
         expect(result, true);
       });
       
       test('isFirstTimeUser should return false after welcome completed', () async {
         SharedPreferences.setMockInitialValues({'has_seen_welcome': true});
         final result = await AppInitializer.isFirstTimeUser();
         expect(result, false);
       });
       
       test('markWelcomeCompleted should set preference value', () async {
         SharedPreferences.setMockInitialValues({});
         await AppInitializer.markWelcomeCompleted();
         final prefs = await SharedPreferences.getInstance();
         expect(prefs.getBool('has_seen_welcome'), true);
       });
     });
   }
   ```

### 2. Widget Tests

1. Test splash screen
   ```dart
   // splash_screen_test.dart
   import 'package:flutter/material.dart';
   import 'package:flutter_test/flutter_test.dart';
   import 'package:provider/provider.dart';
   import 'package:new_lekky/features/splash/presentation/screens/splash_screen.dart';
   import 'package:new_lekky/features/splash/presentation/controllers/splash_controller.dart';
   import 'package:new_lekky/core/constants/app_strings.dart';

   void main() {
     testWidgets('SplashScreen displays correctly', (WidgetTester tester) async {
       await tester.pumpWidget(
         MaterialApp(
           home: ChangeNotifierProvider(
             create: (_) => SplashController(),
             child: SplashScreen(),
           ),
         ),
       );
       
       expect(find.text(AppStrings.appName), findsOneWidget);
       expect(find.text(AppStrings.tagline), findsOneWidget);
       expect(find.text(AppStrings.splashQuote), findsOneWidget);
       expect(find.byType(CircularProgressIndicator), findsOneWidget);
     });
   }
   ```

2. Test welcome screen
   ```dart
   // welcome_screen_test.dart
   import 'package:flutter/material.dart';
   import 'package:flutter_test/flutter_test.dart';
   import 'package:provider/provider.dart';
   import 'package:new_lekky/features/welcome/presentation/screens/welcome_screen.dart';
   import 'package:new_lekky/features/welcome/presentation/controllers/welcome_controller.dart';
   import 'package:new_lekky/core/constants/app_strings.dart';

   void main() {
     testWidgets('WelcomeScreen displays correctly', (WidgetTester tester) async {
       await tester.pumpWidget(
         MaterialApp(
           home: ChangeNotifierProvider(
             create: (_) => WelcomeController(),
             child: WelcomeScreen(),
           ),
         ),
       );
       
       expect(find.text(AppStrings.welcomeTitle), findsOneWidget);
       expect(find.text(AppStrings.welcomeSubtitle), findsOneWidget);
       expect(find.text(AppStrings.trackUsage), findsOneWidget);
       expect(find.text(AppStrings.getAlerts), findsOneWidget);
       expect(find.text(AppStrings.viewHistory), findsOneWidget);
       expect(find.text(AppStrings.calculateCosts), findsOneWidget);
       expect(find.text(AppStrings.getStarted), findsOneWidget);
       expect(find.text(AppStrings.restoreData), findsOneWidget);
     });
   }
   ```

## Deployment Steps

1. Create necessary assets
   - Design and export logo and icon files
   - Place in assets/images/ directory

2. Install dependencies
   ```bash
   flutter pub get
   ```

3. Run tests
   ```bash
   flutter test
   ```

4. Build and run the app
   ```bash
   flutter run
   ```

## Next Steps

1. Implement the Setup screen
2. Implement the Home screen
3. Implement database functionality
4. Implement meter reading and top-up tracking
5. Implement notifications
6. Implement data visualization
7. Implement data export/import functionality
