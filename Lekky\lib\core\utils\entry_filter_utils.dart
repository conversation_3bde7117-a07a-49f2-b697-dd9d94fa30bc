import '../shared/enums/entry_enums.dart';
import '../../features/meter_readings/domain/models/meter_reading.dart';

/// Utility class for filtering entries and excluding dismissed entries
class EntryFilterUtils {
  // Private constructor to prevent instantiation
  EntryFilterUtils._();

  /// Filter out dismissed entries from a list of meter readings
  static List<MeterReading> filterValidMeterReadings(
      List<MeterReading> readings) {
    return readings.where((reading) => !isDismissedEntry(reading)).toList();
  }

  /// Get the last valid (non-dismissed) meter reading
  static MeterReading? getLastValidMeterReading(List<MeterReading> readings) {
    final validReadings = filterValidMeterReadings(readings);
    if (validReadings.isEmpty) return null;

    // Sort by date descending and return the first (most recent)
    validReadings.sort((a, b) => b.date.compareTo(a.date));
    return validReadings.first;
  }

  /// Get the first valid (non-dismissed) meter reading
  static MeterReading? getFirstValidMeterReading(List<MeterReading> readings) {
    final validReadings = filterValidMeterReadings(readings);
    if (validReadings.isEmpty) return null;

    // Sort by date ascending and return the first (oldest)
    validReadings.sort((a, b) => a.date.compareTo(b.date));
    return validReadings.first;
  }

  /// Check if a meter reading is a dismissed entry using simplified criteria
  static bool isDismissedEntry(MeterReading reading) {
    return reading.status == EntryStatus.ignored;
  }

  /// Get the last valid meter reading date
  static DateTime? getLastValidMeterReadingDate(List<MeterReading> readings) {
    final lastReading = getLastValidMeterReading(readings);
    return lastReading?.date;
  }

  /// Get the first valid meter reading date
  static DateTime? getFirstValidMeterReadingDate(List<MeterReading> readings) {
    final firstReading = getFirstValidMeterReading(readings);
    return firstReading?.date;
  }

  /// Get the previous valid meter reading date (second-to-last chronologically)
  static DateTime? getPreviousValidMeterReadingDate(
      List<MeterReading> readings) {
    final validReadings = filterValidMeterReadings(readings);
    if (validReadings.length < 2) return null;

    // Sort by date descending and return the second entry
    validReadings.sort((a, b) => b.date.compareTo(a.date));
    return validReadings[1].date;
  }
}
