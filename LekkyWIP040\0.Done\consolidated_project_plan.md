# NewLekky Consolidated Project Plan

## Executive Summary

The NewLekky app is a prepaid electricity meter tracking application designed for offline use, capable of handling up to 630 meter readings. This document consolidates all project planning information into a comprehensive implementation plan.

## Project Goals

1. Create a robust, efficient, and modular Flutter application for tracking prepaid electricity meter readings
2. Implement a user-friendly interface with intuitive navigation and clear data visualization
3. Ensure data integrity through comprehensive validation rules
4. Provide accurate calculations and projections based on usage patterns
5. Support offline-first operation with efficient data storage and retrieval

## Core Features

- Track meter readings and top-ups with standardized terminology
- Calculate usage averages (recent and total)
- Project remaining days based on usage patterns
- Provide notifications for readings and low balance
- Support data validation and repair
- Offer comprehensive settings customization
- Visualize usage and cost data

## Architecture Overview

The app follows a feature-first modular architecture with clear separation between:

### Layers
- **Presentation Layer**: UI components, screens, and widgets
- **Domain Layer**: Business logic, use cases, and entities
- **Data Layer**: Repositories, data sources, and models

### Folder Structure
- **/screens/**: Screen-level UI components
- **/widgets/**: Reusable UI components
- **/models/**: Data models and entities
- **/providers/**: Riverpod providers for state management
- **/services/**: Service classes for external interactions
- **/utils/**: Utility functions and helpers
- **/theme/**: Theme-related files
- **/core/l10n/**: Internationalization resources

### Technology Stack
- **Framework**: Flutter (latest stable)
- **State Management**: Riverpod for state management and dependency injection
- **Database**: SQLite with sqflite for data persistence
- **Preferences**: SharedPreferences for settings storage
- **Navigation**: go_router
- **Testing**: flutter_test, integration_test

### Architecture Principles
- **Single Responsibility Principle**: Each file should have a single responsibility
- **File Size Limits**: Files should be 300-600 lines maximum
- **Modular Changes**: Preserve existing functionality when making changes
- **Core Environment Provider**: Riverpod provider for environment (dev/staging/prod)
- **Design Tokens**: Spacing, radii, and other design constants in /core/theme/tokens.dart

## Database Schema

The database will consist of the following tables:

1. **meter_readings**
   - `id`: INTEGER PRIMARY KEY
   - `value`: REAL NOT NULL
   - `date`: TEXT NOT NULL (ISO 8601 format)
   - `is_valid`: INTEGER NOT NULL (0 or 1)
   - `notes`: TEXT

2. **top_ups**
   - `id`: INTEGER PRIMARY KEY
   - `amount`: REAL NOT NULL
   - `date`: TEXT NOT NULL (ISO 8601 format)
   - `notes`: TEXT

3. **averages**
   - `id`: INTEGER PRIMARY KEY
   - `recent_average`: REAL
   - `total_average`: REAL
   - `last_updated`: TEXT NOT NULL (ISO 8601 format)

4. **settings**
   - `key`: TEXT PRIMARY KEY
   - `value`: TEXT NOT NULL

## State Management

### Provider Structure
- **Global State Providers**:
  - `CurrentStateProvider`: Main provider for app-wide state
  - `SettingsProvider`: Manages settings and persists them
  - `NotificationsProvider`: Manages notification state
  - `DatabaseProvider`: Provides access to database operations

- **Feature-Specific Providers**:
  - `MeterReadingsProvider`: Manages meter reading data and operations
  - `TopUpsProvider`: Manages top-up data and operations
  - `CalculationsProvider`: Handles complex calculations and derived data
  - `ValidationProvider`: Manages validation rules and error states

## UI/UX Implementation

### Theme Implementation
- Comprehensive light/dark mode support
- Consistent color palette for both modes
- Standardized component styling
- Accessibility considerations

### Core Screens
1. **Home Screen**: Current meter status, usage statistics, quick actions
2. **History Screen**: Filterable entry list with pagination and validation indicators
3. **Cost Screen**: Cost calculations, projections, and visualizations
4. **Settings Screen**: Categorized settings with preference management

### Reusable Components
- Standardized button styles
- Card components with consistent styling
- Input fields with validation support
- Dialog system for modals
- Table components for data display

## Implementation Phases

### Phase 1: Foundation (Weeks 1-4)
- Project setup and architecture
- Database implementation
- State management
- Navigation and core UI components
- Theme system implementation

### Phase 2: Core Features (Weeks 5-12)
- Onboarding and setup
- Home screen and entry system
- Basic settings implementation
- History module
- Cost module

### Phase 3: Advanced Features (Weeks 13-16)
- Data visualization
- Notification system
- Data management
- Advanced calculations

### Phase 4: Polish & Testing (Weeks 17-18)
- Comprehensive testing
- Performance optimization
- Accessibility enhancements
- Final UI refinements

## Technical Implementation Details

### Data Validation Strategy
- **Input validation**: Type checking, range validation, format validation
- **Business rule validation**: Domain-specific rules
- **Cross-field validation**: Relationships between different fields
- **Temporal validation**: Date/time sequence validation

### Specific Validation Rules
- **Meter readings**:
  - Must be positive numbers (≥ 0)
  - Cannot exceed previous reading plus any top-ups
  - Must have chronological dates (no future dates, no out-of-order entries)
  - Should be within reasonable range (warn if > 2x recent average)
- **Top-ups**:
  - Must be positive amounts
  - Must have valid dates (no future dates)
  - Should be within reasonable range (warn if unusually large)

### Error Handling Strategy
- **Domain-specific errors**: Custom error types for each feature module
- **User-facing error handling**: Friendly, actionable error messages
- **Error logging and debugging**: Structured logging with contextual information
- **Critical error management**: Fallback mechanisms and recovery options

### Performance Optimization
- **Database optimization**: Indexed queries, query optimization, transaction batching
- **Calculation optimization**: Incremental calculations, memoization, background processing
- **UI performance**: Virtualized lists, const widgets, widget memoization
- **Memory management**: Proper resource disposal, image optimization, state isolation

## Testing Strategy

### Test Coverage Targets
- Overall code coverage target: 80%
- Core business logic (calculations, validation): 90%+
- UI components: 70%+
- Priority areas: calculation algorithms, data validation, database operations

### Testing Types
1. **Unit Testing**: Test calculation logic, database operations, state management
2. **Widget Testing**: Test UI components, user interactions, screen navigation
3. **Integration Testing**: Test complete user flows, data persistence, notification system
4. **Performance Testing**: Test database operations with 1000+ entries, UI rendering, calculation speed

## App Requirements

The NewLekky app requires:

1. **Error Handling**:
   - Comprehensive error handling throughout the app
   - User-friendly error messages
   - Graceful degradation when features fail
   - Detailed logging for troubleshooting

2. **Data Validation**:
   - Strict validation of all user inputs
   - Validation of imported data
   - Real-time feedback on validation issues
   - Tools for repairing invalid data

3. **Architecture**:
   - Offline-first design
   - Modular, maintainable code structure
   - Clear separation of concerns
   - Consistent state management

4. **Performance**:
   - Optimization for up to 630 entries
   - Efficient database queries
   - Responsive UI even with large datasets
   - Minimal memory footprint

5. **Security**:
   - Encrypted backups
   - Optional app lock with PIN/biometric authentication
   - Secure storage of sensitive data
   - Privacy-focused design

## Risk Management

| Risk | Probability | Impact | Mitigation Strategy |
|------|------------|--------|---------------------|
| Performance issues with large datasets | Medium | High | Implement pagination, indexing, and caching; test with oversized datasets |
| Complex calculation accuracy | Medium | High | Create comprehensive unit tests; validate with real-world scenarios |
| UI inconsistencies across devices | Medium | Medium | Use responsive design patterns; test on multiple device sizes |
| Data migration failures | Low | High | Implement backup before migration; provide manual recovery options |
| Notification reliability issues | Medium | Medium | Create fallback mechanisms; provide in-app notification center |

## Detailed Implementation Plan

### Phase 1: Foundation (Weeks 1-4)

#### Week 1: Project Setup and Architecture
- **Day 1-2: Project Initialization**
  - Create Flutter project with proper folder structure
  - Configure essential dependencies (Riverpod, go_router, sqflite, get_it)
  - Set up linting and code formatting rules
  - Configure version control and CI/CD pipeline

- **Day 3-4: Core Architecture**
  - Implement feature-first folder structure
  - Set up dependency injection with get_it and injectable
  - Create base classes for presentation, domain, and data layers
  - Establish error handling framework

- **Day 5: Design System Foundation**
  - Create theme configuration (colors, typography, spacing)
  - Implement theme service with light/dark mode support
  - Set up responsive layout utilities

#### Week 2: Database Implementation
- **Day 1-2: Database Models**
  - Create data models for meter readings, top-ups, and settings
  - Implement entity mappers between domain and data layers
  - Set up database schema with proper relationships

- **Day 3-4: Database Access Layer**
  - Implement database helper with optimized schema
  - Create repository interfaces in domain layer
  - Implement repositories with proper error handling
  - Set up migration system for future updates

- **Day 5: Data Validation Framework**
  - Implement validation rules for meter readings and top-ups
  - Create validation service for centralized validation logic
  - Set up error reporting for validation failures

#### Week 3: State Management
- **Day 1-2: Provider Setup**
  - Implement CurrentStateProvider for global app state
  - Create SettingsProvider for user preferences
  - Set up DatabaseProvider for data access
  - Implement NotificationsProvider for alerts

- **Day 3-4: State Persistence**
  - Implement settings persistence using SharedPreferences
  - Create state hydration/rehydration mechanism
  - Set up state synchronization between providers

- **Day 5: State Testing**
  - Create unit tests for state management
  - Test state persistence and recovery
  - Verify provider interactions

#### Week 4: Navigation and Core UI
- **Day 1-2: Navigation System**
  - Implement go_router configuration
  - Set up route definitions and transitions
  - Create navigation service for programmatic navigation
  - Implement deep linking support

- **Day 3-4: Core UI Components**
  - Create reusable button components
  - Implement card components for data display
  - Develop input fields with validation support
  - Create dialog system for modals

- **Day 5: Splash and Welcome Screens**
  - Implement splash screen with initialization logic
  - Create welcome screen for first-time users
  - Set up permission handling

### Phase 2: Core Features (Weeks 5-12)

#### Week 5-6: Onboarding and Setup
- Implement welcome flow for first-time users
- Create setup screens for initial configuration
- Develop first-time meter reading entry
- Implement data restoration functionality

#### Week 7-8: Home Screen and Entry System
- Create home screen with meter status display
- Implement add/edit entry dialogs
- Develop entry validation with real-time feedback
- Create basic calculation service

#### Week 9: Basic Settings Implementation
- Implement settings screen with categories
- Create preference management system
- Develop theme switching functionality
- Implement language selection

#### Week 10-11: History Module
- Create history screen with pagination
- Implement filtering and sorting functionality
- Develop entry management (view, edit, delete)
- Add validation indicators for problematic entries

#### Week 12: Cost Module
- Implement cost calculation algorithms
- Create cost projection functionality
- Develop cost breakdown views
- Add time period selection

### Phase 3: Advanced Features (Weeks 13-16)

#### Week 13: Data Visualization
- Implement line charts for usage trends
- Create bar charts for cost breakdowns
- Develop interactive chart components
- Add time period selection for visualizations

#### Week 14: Notification System
- Implement local notification framework
- Create reminder scheduling system
- Develop alert generation for low balance
- Add notification preferences management

#### Week 15: Data Management
- Implement data export/import functionality
- Create backup and restore system
- Develop data validation dashboard
- Add data repair tools

#### Week 16: Advanced Calculations
- Implement seasonal adjustment algorithms
- Create projection accuracy indicators
- Develop comparative analysis features
- Add advanced usage statistics

### Phase 4: Polish & Testing (Weeks 17-18)

#### Week 17: Testing and Optimization
- Implement comprehensive unit tests
- Create widget tests for UI components
- Develop integration tests for key user flows
- Perform performance optimization

#### Week 18: Final Polish
- Enhance accessibility features
- Refine animations and transitions
- Implement final UI adjustments
- Prepare for release

## Success Criteria

1. App handles up to 630 meter readings efficiently
2. All validation rules are properly implemented
3. Calculations are accurate and reliable
4. UI is responsive and consistent across devices
5. App works completely offline
6. Test coverage meets or exceeds targets
7. Performance benchmarks are achieved

## Next Steps

1. Finalize dependency list and versions
2. Set up development environment
3. Create initial project structure
4. Begin implementation of Foundation phase
5. Establish regular progress reviews
