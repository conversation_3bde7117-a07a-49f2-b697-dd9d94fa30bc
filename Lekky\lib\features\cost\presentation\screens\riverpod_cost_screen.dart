import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/cost_provider.dart';
import '../widgets/cost_summary_card.dart';

import '../widgets/recent_average_chart_card.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/theme/app_colors.dart';

import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/preference_state.dart';
import '../../domain/models/cost_state.dart';

/// Riverpod-based Cost Analysis screen demonstrating the migration pattern
/// This shows how to use the new CostProvider with reactive cost calculations
class RiverpodCostScreen extends ConsumerWidget {
  const RiverpodCostScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final costAsync = ref.watch(costProvider);

    return Scaffold(
      body: Column(
        children: [
          // App Banner with "Cost" title using gradient colors
          AppBanner(
            message: 'Cost',
            gradientColors: AppColors.getCostMainCardGradient(
                Theme.of(context).brightness == Brightness.dark),
            textColor: AppColors.getAppBarTextColor(
                'cost', Theme.of(context).brightness == Brightness.dark),
          ),

          // Main content based on async state
          Expanded(
            child: costAsync.when(
              data: (costState) => _buildCostContent(
                context,
                ref,
                costState,
              ),
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to load cost analysis',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(costProvider),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the main cost content
  Widget _buildCostContent(
    BuildContext context,
    WidgetRef ref,
    CostState costState,
  ) {
    if (costState.hasError) {
      return Column(
        children: [
          // Error banner
          AppBanner(
            message: costState.errorMessage!,
            backgroundColor: Colors.red,
            onDismiss: () {
              ref.read(costProvider.notifier).clearError();
              ref.read(costProvider.notifier).refresh();
            },
          ),
          const Expanded(
            child: Center(
              child: Text('Please try refreshing the cost analysis'),
            ),
          ),
        ],
      );
    }

    // Skip controls section and go directly to the new card layout
    return RefreshIndicator(
      onRefresh: () => ref.read(costProvider.notifier).refresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: _buildCostCards(context, ref, costState),
      ),
    );
  }

  /// Build cost analysis cards
  Widget _buildCostCards(
    BuildContext context,
    WidgetRef ref,
    CostState costState,
  ) {
    if (costState.isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (!costState.hasCostResult) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No cost data available',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Add some meter readings to see cost analysis',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Get settings for currency symbol
    final settingsAsync = ref.watch(settingsProvider);

    return settingsAsync.when(
      data: (settings) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cost Summary Card with dynamic currency and calculation method
          CostSummaryCard(
            title: 'Cost of Electric ${settings.currencySymbol}',
            value: costState.costResult!.formattedCostPerPeriod,
            subtitle: costState.costResult!
                .getFormattedDescription(costState.averageType),
            icon: Icons.analytics,
          ),
          const SizedBox(height: 16),

          // Recent Average Chart Card (third card)
          RecentAverageChartCard(
            chartData: costState.recentAverageChartData,
            currencySymbol: settings.currencySymbol,
            preferences: PreferenceState(
              currencySymbol: settings.currencySymbol,
              alertThreshold: settings.alertThreshold,
              daysInAdvance: settings.daysInAdvance,
              notificationsEnabled: settings.lowBalanceAlertsEnabled ||
                  settings.timeToTopUpAlertsEnabled ||
                  settings.invalidRecordAlertsEnabled,
              lowBalanceAlertsEnabled: settings.lowBalanceAlertsEnabled,
              timeToTopUpAlertsEnabled: settings.timeToTopUpAlertsEnabled,
              invalidRecordAlertsEnabled: settings.invalidRecordAlertsEnabled,
            ),
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(
        child: Text('Error loading preferences: $error'),
      ),
    );
  }
}
