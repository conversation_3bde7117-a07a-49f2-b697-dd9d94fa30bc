import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:path_provider/path_provider.dart';
import 'package:media_store_plus/media_store_plus.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/constants/database_constants.dart';
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/permission_helper.dart';
import '../../../core/di/service_locator.dart';
import '../../../core/providers/progress_provider.dart';
import '../../../core/services/platform_service.dart';

/// Export result class for better error handling
class ExportResult {
  final bool isSuccess;
  final String? filePath;
  final String? errorMessage;

  const ExportResult({
    required this.isSuccess,
    this.filePath,
    this.errorMessage,
  });

  factory ExportResult.success(String filePath) {
    return ExportResult(isSuccess: true, filePath: filePath);
  }

  factory ExportResult.failure(String errorMessage, {String? tempFilePath}) {
    return ExportResult(
      isSuccess: false,
      errorMessage: errorMessage,
      filePath: tempFilePath,
    );
  }
}

/// File verification result
class _FileVerificationResult {
  final bool isValid;
  final int? fileSize;
  final String? error;

  const _FileVerificationResult({
    required this.isValid,
    this.fileSize,
    this.error,
  });
}

/// Provider for export data state
final exportDataProvider =
    StateNotifierProvider<ExportDataNotifier, ExportDataState>((ref) {
  final progressNotifier = ref.watch(exportProgressProvider.notifier);
  return ExportDataNotifier(progressNotifier);
});

/// Export data state
class ExportDataState {
  final bool isExporting;
  final bool isCompleted;
  final bool isSuccessful;
  final String? errorMessage;
  final String? filePath;

  const ExportDataState({
    this.isExporting = false,
    this.isCompleted = false,
    this.isSuccessful = false,
    this.errorMessage,
    this.filePath,
  });

  ExportDataState copyWith({
    bool? isExporting,
    bool? isCompleted,
    bool? isSuccessful,
    String? errorMessage,
    String? filePath,
  }) {
    return ExportDataState(
      isExporting: isExporting ?? this.isExporting,
      isCompleted: isCompleted ?? this.isCompleted,
      isSuccessful: isSuccessful ?? this.isSuccessful,
      errorMessage: errorMessage ?? this.errorMessage,
      filePath: filePath ?? this.filePath,
    );
  }
}

/// Export data notifier
class ExportDataNotifier extends StateNotifier<ExportDataState> {
  ExportDataNotifier(this._progressNotifier) : super(const ExportDataState());

  static const String _backupFilename = 'lekky_export_101.csv';
  static const int _backupFormatVersion = 101;

  final PermissionHelper _permissionHelper = PermissionHelper();
  final ProgressNotifier _progressNotifier;
  final ProgressCalculator _progressCalculator =
      ProgressCalculator(ExportProgressSteps.steps);

  /// Export data with Android version-aware strategy for maximum compatibility
  Future<void> exportData() async {
    state = state.copyWith(isExporting: true, isCompleted: false);

    try {
      Logger.info('Starting CSV export process');

      // Start progress tracking
      _progressNotifier.startOperation(
        operationType: 'export',
        initialMessage: 'Preparing export...',
        canCancel: true,
      );

      // Get database helper
      final databaseHelper = serviceLocator<DatabaseHelper>();
      final db = await databaseHelper.database;

      // Step 1: Prepare data
      _progressCalculator.updateStep('prepare', 0.5);
      _progressNotifier.updateProgress(
        progress: _progressCalculator.calculateOverallProgress(),
        statusMessage: 'Retrieving data from database...',
      );

      // Get all meter readings and top-ups
      final meterReadings =
          await db.query(DatabaseConstants.meterReadingsTable);
      final topUps = await db.query(DatabaseConstants.topUpsTable);

      final totalEntries = meterReadings.length + topUps.length;
      Logger.info(
          'Retrieved ${meterReadings.length} meter readings and ${topUps.length} top-ups (total: $totalEntries)');

      _progressCalculator.updateStep('prepare', 1.0);
      _progressNotifier.updateProgress(
        progress: _progressCalculator.calculateOverallProgress(),
        statusMessage: 'Converting $totalEntries entries...',
        metadata: {'totalEntries': totalEntries},
      );

      // Convert to MeterEntry objects
      final entries = <MeterEntry>[];

      // Add meter readings
      for (final reading in meterReadings) {
        // Check if this is a dismissal entry (status = 2)
        final status = reading['status'] as int? ??
            ((reading['is_valid'] as int?) == 1 ? 0 : 1);
        final typeCode =
            status == 2 ? 2 : 0; // Use type 2 for dismissed entries in CSV

        entries.add(MeterEntry(
          id: reading['id'] as int?,
          date: DateTime.parse(reading['date'] as String),
          reading: status == 2 ? 0 : reading['value'] as double,
          amountToppedUp: 0,
          typeCode: typeCode,
          notes: reading['notes'] as String?,
        ));
      }

      // Add top-ups
      for (final topUp in topUps) {
        entries.add(MeterEntry(
          id: topUp['id'] as int?,
          date: DateTime.parse(topUp['date'] as String),
          reading: 0,
          amountToppedUp: topUp['amount'] as double,
          typeCode: 1,
          notes: topUp['notes'] as String?,
        ));
      }

      // Sort entries by date
      entries.sort((a, b) => a.date.compareTo(b.date));

      // Step 2: Generate CSV
      _progressCalculator.updateStep('generate', 0.3);
      _progressNotifier.updateProgress(
        progress: _progressCalculator.calculateOverallProgress(),
        statusMessage: 'Generating CSV format...',
      );

      // Create CSV data with version header
      final csvData = [
        ['# Lekky v1.0.1 BackupFormat=$_backupFormatVersion'],
        ['Date', 'Type', 'Amount'],
        ...entries.map((entry) => [
              entry.timestamp.toIso8601String(),
              entry.typeCode.toString(),
              entry.amountToppedUp > 0
                  ? entry.amountToppedUp.toString()
                  : entry.reading.toString(),
            ]),
      ];

      _progressCalculator.updateStep('generate', 0.8);
      _progressNotifier.updateProgress(
        progress: _progressCalculator.calculateOverallProgress(),
        statusMessage: 'Converting to CSV string...',
      );

      // Convert to CSV string
      final csv = const ListToCsvConverter().convert(csvData);

      _progressCalculator.updateStep('generate', 1.0);
      _progressNotifier.updateProgress(
        progress: _progressCalculator.calculateOverallProgress(),
        statusMessage: 'Determining save location...',
      );

      // Step 3: Save file
      _progressCalculator.updateStep('save', 0.1);
      _progressNotifier.updateProgress(
        progress: _progressCalculator.calculateOverallProgress(),
        statusMessage: 'Saving file...',
      );

      // Determine export strategy based on platform and Android version
      final exportResult = await _executeVersionAwareExport(csv);

      _progressCalculator.updateStep('save', 1.0);

      if (exportResult.isSuccess && exportResult.filePath != null) {
        // Step 4: Verify file
        _progressCalculator.updateStep('verify', 0.5);
        _progressNotifier.updateProgress(
          progress: _progressCalculator.calculateOverallProgress(),
          statusMessage: 'Verifying file...',
        );

        final verificationResult =
            await _verifyExportedFile(exportResult.filePath!, csv);

        _progressCalculator.updateStep('verify', 1.0);

        if (verificationResult.isValid) {
          Logger.info(
              'Data exported and verified successfully to: ${exportResult.filePath}');
          _progressNotifier.completeSuccess(
            successMessage: 'Export completed successfully',
            metadata: {
              'filePath': exportResult.filePath,
              'fileSize': verificationResult.fileSize,
              'entryCount': totalEntries,
            },
          );

          state = state.copyWith(
            isExporting: false,
            isCompleted: true,
            isSuccessful: true,
            filePath: exportResult.filePath,
          );
        } else {
          Logger.error('File verification failed: ${verificationResult.error}');
          _progressNotifier.completeFailure(
            errorMessage:
                'File verification failed: ${verificationResult.error}',
          );

          state = state.copyWith(
            isExporting: false,
            isCompleted: true,
            isSuccessful: false,
            errorMessage:
                'Export completed but file verification failed: ${verificationResult.error}',
            filePath: exportResult.filePath,
          );
        }
      } else {
        Logger.warning('Export failed: ${exportResult.errorMessage}');
        _progressNotifier.completeFailure(
          errorMessage: exportResult.errorMessage ?? 'Export failed',
        );

        state = state.copyWith(
          isExporting: false,
          isCompleted: true,
          isSuccessful: false,
          errorMessage: exportResult.errorMessage,
          filePath: exportResult.filePath,
        );
      }
    } catch (e) {
      Logger.error('Failed to export data: $e');

      _progressNotifier.completeFailure(
        errorMessage: 'Export failed: ${e.toString()}',
      );

      state = state.copyWith(
        isExporting: false,
        isCompleted: true,
        isSuccessful: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// Verify exported file exists and has correct content
  Future<_FileVerificationResult> _verifyExportedFile(
      String filePath, String expectedContent) async {
    try {
      // Check if this is a content URI (Android 15+ media store)
      if (filePath.startsWith('content://')) {
        Logger.info('Verifying content URI: $filePath');

        // For content URIs, we can't use File operations directly
        // Instead, we'll do a basic validation that the URI was returned
        // and assume the MediaStore operation was successful
        final expectedSize = expectedContent.length;

        Logger.info(
            'Content URI verification successful: $filePath (expected: $expectedSize bytes)');
        return _FileVerificationResult(
          isValid: true,
          fileSize: expectedSize,
        );
      }

      // Traditional file path verification for older Android versions
      final file = File(filePath);

      // Check if file exists
      if (!await file.exists()) {
        return const _FileVerificationResult(
          isValid: false,
          error: 'File does not exist at specified path',
        );
      }

      // Check file size
      final fileSize = await file.length();
      if (fileSize == 0) {
        return const _FileVerificationResult(
          isValid: false,
          fileSize: 0,
          error: 'File is empty',
        );
      }

      // Verify content matches (basic check)
      final actualContent = await file.readAsString();
      if (actualContent.length != expectedContent.length) {
        return _FileVerificationResult(
          isValid: false,
          fileSize: fileSize,
          error:
              'File content length mismatch (expected: ${expectedContent.length}, actual: ${actualContent.length})',
        );
      }

      // Check if it starts with the correct header
      if (!actualContent.startsWith('# Lekky v1.0.1 BackupFormat=')) {
        return _FileVerificationResult(
          isValid: false,
          fileSize: fileSize,
          error: 'File does not contain expected Lekky backup header',
        );
      }

      Logger.info('File verification successful: $filePath ($fileSize bytes)');
      return _FileVerificationResult(
        isValid: true,
        fileSize: fileSize,
      );
    } catch (e) {
      Logger.error('File verification error: $e');
      return _FileVerificationResult(
        isValid: false,
        error: 'Verification failed: ${e.toString()}',
      );
    }
  }

  /// Execute version-aware export strategy based on platform and Android version
  Future<ExportResult> _executeVersionAwareExport(String csv) async {
    try {
      final platformInfo = await PlatformService.getPlatformInfo();
      Logger.info('Platform detected: ${platformInfo.userFriendlyDescription}');

      if (platformInfo.isIOS) {
        Logger.info('iOS detected - using Storage Access Framework');
        final filePath = await _tryStorageAccessFramework(csv);
        if (filePath != null) {
          return ExportResult.success(filePath);
        }
        return ExportResult.failure(
            'Could not save file on iOS. Please try again.');
      }

      if (platformInfo.isAndroid && platformInfo.androidSdkInt != null) {
        final sdkInt = platformInfo.androidSdkInt!;
        Logger.info('Android API level detected: $sdkInt');

        // For Android 15+ (API 35+): Use media_store_plus as primary method
        if (sdkInt >= 35) {
          Logger.info('Android 15+ detected - using media_store_plus');

          // Try media_store_plus for modern Android file access
          String? filePath = await _tryMediaStorePlus(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Fallback to SAF for user-controlled location
          Logger.info('MediaStore failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          Logger.warning(
              'Both methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder. File saved to app storage. You can share it from the notification or find it in your file manager under Android/data/com.lekky.app/files/',
            tempFilePath: tempPath,
          );
        }

        // For Android 14 (API 34): Use media_store_plus as primary method
        else if (sdkInt >= 34) {
          Logger.info('Android 14 detected - using media_store_plus');

          // Try media_store_plus for modern Android file access
          String? filePath = await _tryMediaStorePlus(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Fallback to traditional method
          Logger.info('MediaStore failed - trying traditional method');
          filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to SAF
          Logger.info(
              'Traditional method failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          Logger.warning('All methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder. File saved to app storage. You can share it from the notification or find it in your file manager under Android/data/com.lekky.app/files/',
            tempFilePath: tempPath,
          );
        }

        // For Android 10-13 (API 29-33): Prioritize Downloads folder with media_store_plus fallback
        else if (sdkInt >= 29) {
          Logger.info('Android 10-13 detected - trying Downloads folder first');

          // Try traditional Downloads folder access first
          String? filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Fallback to media_store_plus
          Logger.info('Downloads folder failed - trying media_store_plus');
          filePath = await _tryMediaStorePlus(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to SAF for user-controlled location
          Logger.info('MediaStore failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          Logger.warning('All methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder. File saved to app storage. You can share it from the notification or find it in your file manager under Android/data/com.lekky.app/files/',
            tempFilePath: tempPath,
          );
        }

        // For Android 8.0.0 and older (API 26 and below): Prioritize traditional method
        else if (sdkInt >= 21) {
          Logger.info(
              'Android 5.0-9.0 detected - using traditional file access');

          // Try traditional method first (more reliable on older Android)
          String? filePath = await _tryTraditionalMethod(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Try SAF as fallback (limited support on older versions)
          Logger.info(
              'Traditional method failed - trying Storage Access Framework');
          filePath = await _tryStorageAccessFramework(csv);
          if (filePath != null) {
            return ExportResult.success(filePath);
          }

          // Final fallback to app-specific storage
          Logger.warning(
              'Both methods failed - creating temp file for sharing');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Could not save to Downloads folder due to Android version limitations. File saved to app storage. Please use the Share option to save your backup to your preferred location.',
            tempFilePath: tempPath,
          );
        }

        // For very old Android versions (below API 21)
        else {
          Logger.warning(
              'Very old Android version detected (API $sdkInt) - limited export options');
          final tempPath = await _createTempFile(csv);
          return ExportResult.failure(
            'Your Android version has limited file access. File saved to app storage. Please use the Share option to save your backup.',
            tempFilePath: tempPath,
          );
        }
      }

      // Fallback for unknown platforms
      Logger.warning('Unknown platform detected - using fallback method');
      final tempPath = await _createTempFile(csv);
      return ExportResult.failure(
        'Platform not fully supported. File saved to temporary storage. Please use the Share option.',
        tempFilePath: tempPath,
      );
    } catch (e) {
      Logger.error('Error in version-aware export: $e');
      try {
        final tempPath = await _createTempFile(csv);
        return ExportResult.failure(
          'Export error occurred. File saved to temporary storage. Error: ${e.toString()}',
          tempFilePath: tempPath,
        );
      } catch (tempError) {
        Logger.error('Failed to create temp file: $tempError');
        return ExportResult.failure(
            'Export completely failed: ${e.toString()}');
      }
    }
  }

  /// Try using Storage Access Framework to save the file
  Future<String?> _tryStorageAccessFramework(String csv) async {
    try {
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Backup File',
        fileName: _backupFilename,
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null) {
        final file = File(result);
        await file.writeAsString(csv);
        Logger.info('Data exported using Storage Access Framework: $result');
        return result;
      }
    } catch (e) {
      Logger.warning('Storage Access Framework failed: $e');
    }
    return null;
  }

  /// Try traditional method with permissions
  Future<String?> _tryTraditionalMethod(String csv) async {
    try {
      final hasPermission =
          await _permissionHelper.checkAndRequestStoragePermission();
      if (hasPermission) {
        final downloadsPath = await _getDownloadsPath();
        if (downloadsPath != null) {
          final path = '$downloadsPath/$_backupFilename';
          final file = File(path);
          await file.writeAsString(csv);
          Logger.info('Data exported using traditional method: $path');
          return path;
        }
      }
    } catch (e) {
      Logger.warning('Traditional method failed: $e');
    }
    return null;
  }

  /// Create temporary file as fallback
  Future<String> _createTempFile(String csv) async {
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/$_backupFilename');
    await tempFile.writeAsString(csv);
    return tempFile.path;
  }

  /// Get Downloads folder path for traditional method with fallback to app-specific storage
  Future<String?> _getDownloadsPath() async {
    try {
      if (Platform.isAndroid) {
        final directory = await getExternalStorageDirectory();
        if (directory != null) {
          // Try to access public Downloads folder with Lekky subfolder
          try {
            const downloadsDir = '/storage/emulated/0/Download/Lekky';

            final dir = Directory(downloadsDir);
            if (!await dir.exists()) {
              await dir.create(recursive: true);
            }

            // Test write access
            final testFile = File('$downloadsDir/.lekky_test');
            await testFile.writeAsString('test');
            await testFile.delete();

            Logger.info('Downloads folder access confirmed: $downloadsDir');
            return downloadsDir;
          } catch (e) {
            Logger.warning('Cannot access public Downloads folder: $e');

            // Fallback to app-specific external storage
            final appExternalDir = directory.path;
            final dir = Directory(appExternalDir);
            if (!await dir.exists()) {
              await dir.create(recursive: true);
            }

            Logger.info('Using app-specific external storage: $appExternalDir');
            return appExternalDir;
          }
        }
      }

      // iOS fallback
      final directory = await getApplicationDocumentsDirectory();
      Logger.info('Using iOS documents directory: ${directory.path}');
      return directory.path;
    } catch (e) {
      Logger.error('Error getting downloads path: $e');
      return null;
    }
  }

  /// Try media_store_plus method for Android 14+ file access
  Future<String?> _tryMediaStorePlus(String csv) async {
    try {
      Logger.info('Attempting media_store_plus export...');

      // Create temporary file
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$_backupFilename');
      await tempFile.writeAsString(csv);

      // Initialize MediaStore and set app folder
      await MediaStore.ensureInitialized();
      MediaStore.appFolder = "Lekky";

      // Save to Downloads folder using MediaStore API
      final mediaStore = MediaStore();
      final saveInfo = await mediaStore.saveFile(
        tempFilePath: tempFile.path,
        dirType: DirType.download,
        dirName: DirName.download,
      );

      // Clean up temporary file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }

      if (saveInfo != null) {
        final fileUri = saveInfo.uri;
        final filePath = fileUri.toString();
        if (filePath.isNotEmpty) {
          Logger.info('Data exported using media_store_plus: $filePath');
          return filePath;
        }
      }

      Logger.warning('MediaStore saveFile returned null or empty');
      return null;
    } catch (e) {
      Logger.warning('MediaStore method failed: $e');
      return null;
    }
  }

  /// Reset the export state
  void resetState() {
    state = const ExportDataState();
  }
}
