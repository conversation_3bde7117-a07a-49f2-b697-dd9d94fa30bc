import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/error_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/di/service_locator.dart';
import '../../domain/models/cost_state.dart';
import '../../domain/models/cost_period.dart';
import '../../domain/models/cost_mode.dart';
import '../../domain/models/date_range.dart';
import '../../data/cost_repository.dart';

import '../services/cost_calculation_service.dart';
import '../services/cost_chart_service.dart';
import '../services/cost_date_service.dart';

part 'cost_provider.g.dart';

/// Cost repository provider
@riverpod
CostRepository costRepository(CostRepositoryRef ref) {
  return serviceLocator<CostRepository>();
}

/// Cost analysis provider with comprehensive cost calculation and chart management
@riverpod
class Cost extends _$Cost {
  StreamSubscription<EventType>? _eventSubscription;

  @override
  Future<CostState> build() async {
    // Set up event listener for data updates
    _setupEventListener();

    // Dispose event subscription when provider is disposed
    ref.onDispose(() {
      _eventSubscription?.cancel();
    });

    return await _loadInitialState();
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel(); // Cancel any existing subscription
    _eventSubscription = EventBus().stream.listen((event) {
      Logger.info('CostProvider: Received event: $event');
      if (event == EventType.dataUpdated) {
        Logger.info(
            'CostProvider: Received data update event, refreshing cost analysis');
        // Refresh the cost analysis when data is updated
        refresh();
      } else if (event == EventType.averagesCalculating) {
        Logger.info(
            'CostProvider: Averages calculating, showing loading state');
        final currentState = state.value ?? CostState.initial();
        state = AsyncValue.data(currentState.copyWith(isLoading: true));
      } else if (event == EventType.averageCalculationFailed) {
        Logger.error('CostProvider: Average calculation failed');
        final currentState = state.value ?? CostState.initial();
        state = AsyncValue.data(currentState.copyWith(
          isLoading: false,
          errorMessage: 'Failed to calculate averages. Please try again.',
        ));
      } else if (event == EventType.currencyUpdated) {
        Logger.info('CostProvider: Currency updated, refreshing cost analysis');
        // Refresh the cost analysis when currency is updated
        refresh();
      }
    });
  }

  /// Load initial state
  Future<CostState> _loadInitialState() async {
    try {
      final initialState = CostState.initial();
      await _updatePeriod(initialState.selectedPeriod, initialState);
      return state.value ?? initialState;
    } catch (error, stackTrace) {
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Cost analysis initialization',
          );
      return CostState.initial().copyWith(
        isLoading: false,
        errorMessage: 'Failed to initialize cost analysis: ${error.toString()}',
      );
    }
  }

  /// Toggle cost mode between past and future
  Future<void> toggleCostMode() async {
    final currentState = state.value ?? CostState.initial();
    final newMode = currentState.costMode == CostMode.past
        ? CostMode.future
        : CostMode.past;

    // Update the selected period based on the new mode
    final periodName = currentState.selectedPeriod.name;
    final newPeriod =
        CostPeriod.getPeriodByNameAndMode(periodName, newMode == CostMode.past);

    state = AsyncValue.data(currentState.copyWith(
      costMode: newMode,
      selectedPeriod: newPeriod,
      isLoading: true,
    ));

    // Recalculate cost with new mode
    await _updatePeriod(newPeriod, state.value!);
  }

  /// Update selected period
  Future<void> updatePeriod(CostPeriod period) async {
    final currentState = state.value ?? CostState.initial();
    if (currentState.selectedPeriod == period) return;

    // Handle custom period selection with default dates
    if (period == CostPeriod.custom) {
      await _handleCustomPeriodSelection(currentState);
      return;
    }

    state = AsyncValue.data(currentState.copyWith(
      selectedPeriod: period,
      isLoading: true,
    ));

    await _updatePeriod(period, state.value!);
  }

  /// Set custom date range
  Future<void> setCustomDateRange(DateTime? fromDate, DateTime? toDate) async {
    final currentState = state.value ?? CostState.initial();

    // Update state with validated date range using service
    final newState = CostDateService.updateStateWithDateRange(
      currentState,
      fromDate: fromDate,
      toDate: toDate,
    ).copyWith(selectedPeriod: CostPeriod.custom);

    state = AsyncValue.data(newState);

    // Only calculate cost if date range is valid
    if (newState.dateRangeError == null) {
      await _calculateCost(newState);
    }
  }

  /// Refresh cost analysis
  Future<void> refresh() async {
    final currentState = state.value ?? CostState.initial();
    state = AsyncValue.data(currentState.copyWith(isLoading: true));
    await _updatePeriod(currentState.selectedPeriod, state.value!);
  }

  /// Clear error
  void clearError() {
    final currentState = state.value ?? CostState.initial();
    state = AsyncValue.data(currentState.copyWith(errorMessage: null));
  }

  /// Handle custom period selection with default dates and insufficient data check
  Future<void> _handleCustomPeriodSelection(CostState currentState) async {
    final costRepo = ref.read(costRepositoryProvider);
    final newState = await CostDateService.handleCustomPeriodSelection(
      ref,
      costRepo,
      currentState,
    );

    state = AsyncValue.data(newState);

    // Auto-trigger cost calculation if valid date range
    if (newState.dateRangeError == null &&
        newState.fromDate != null &&
        newState.toDate != null) {
      await _calculateCost(newState);
    }
  }

  /// Update period and recalculate cost
  Future<void> _updatePeriod(CostPeriod period, CostState currentState) async {
    try {
      if (period != CostPeriod.custom) {
        // Update date range based on the selected period
        final dateRange = DateRange.forPeriod(period);
        final fromDate = dateRange.startDate;
        final toDate = dateRange.endDate;

        // Update state with new date range
        state = AsyncValue.data(currentState.copyWith(
          dateRange: dateRange,
          fromDate: fromDate,
          toDate: toDate,
          dateRangeError: null,
        ));

        // Calculate cost for the period
        await _calculateCost(state.value!);
      } else {
        // For custom period, use existing date range
        await _calculateCost(state.value!);
      }
    } catch (error, stackTrace) {
      Logger.error('Failed to update period: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to calculate cost: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Cost period update',
          );
    }
  }

  /// Calculate cost for custom date range
  Future<void> _calculateCost(CostState currentState) async {
    try {
      final costRepo = ref.read(costRepositoryProvider);

      // Use calculation service to get cost result
      final costResult = await CostCalculationService.calculateForMode(
        ref,
        costRepo,
        currentState,
      );

      // Generate chart data using chart service
      final chartData =
          await CostChartService.generateChartData(ref, currentState);

      // Load recent average chart data
      final recentAverageChartData =
          await CostChartService.loadRecentAverageChartData(ref, currentState);

      // Determine average type based on cost mode
      final averageType = currentState.costMode == CostMode.past
          ? "Recent Average"
          : "Total Average";

      if (costResult != null) {
        Logger.info(
            'CostProvider: ${CostCalculationService.getCostCalculationSummary(costResult)}');
      }

      state = AsyncValue.data(currentState.copyWith(
        costResult: costResult,
        chartData: chartData,
        recentAverageChartData: recentAverageChartData,
        averageType: averageType,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to calculate cost: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to calculate cost: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Cost calculation',
          );
    }
  }
}
